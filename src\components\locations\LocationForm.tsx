import React, { useEffect, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import Checkbox from '../form/input/Checkbox';
import { ErrorDisplay } from '../error';
import { useCreateLocation, useUpdateLocation } from '../../hooks/useLocations';
import { Location, LocationCreateRequest } from '../../types';
import { PlusIcon, TrashBinIcon, TimeIcon } from '../../icons';
import { getUserTimezone } from '../../utils/timezone';

// Opening hours schema matching API
const openingHoursSchema = z.object({
  dayOfWeek: z.string().min(1, "Day of week is required"),
  isActive: z.boolean().default(true),
  hours: z.array(z.object({
    timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
    timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
  })),
});

// Validation schema
const locationSchema = z.object({
  name: z.string().min(1, "Location name is required"),
  shortName: z.string().max(50, 'Short name too long').optional(),
  address: z.string().max(500, 'Address too long').optional(),
  city: z.string().max(100, 'City name too long').optional(),
  mobile: z.string().min(1, "Mobile number is required"),
  isMobileHidden: z.boolean().default(false),
  fax: z.string().max(50, 'Fax too long').optional(),
  floor: z.string().max(50, 'Floor too long').optional(),
  parking: z.boolean().default(false),
  elevator: z.boolean().default(false),
  handicapAccess: z.boolean().default(false),
  timezone: z.string().max(50, 'Timezone too long').optional(),
  // Address details (flat structure as per API)
  country: z.string().max(100, 'Country name too long').default('Algeria'),
  postalCode: z.string().max(20, 'Postal code too long').optional(),
  latitude: z.number().min(-90, "Invalid latitude").max(90, "Invalid latitude"),
  longitude: z.number().min(-180, "Invalid longitude").max(180, "Invalid longitude"),
  // Opening hours
  openingHours: z.array(openingHoursSchema).optional(),
});

type LocationFormData = z.infer<typeof locationSchema>;

interface LocationFormProps {
  location?: Location | null;
  onClose: () => void;
  onSuccess: () => void;
}

// Days of the week
const DAYS_OF_WEEK = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
];

const timezones = [
  // Africa
  'Africa/Abidjan',
  'Africa/Accra',
  'Africa/Addis_Ababa',
  'Africa/Algiers',
  'Africa/Asmara',
  'Africa/Bamako',
  'Africa/Bangui',
  'Africa/Banjul',
  'Africa/Bissau',
  'Africa/Blantyre',
  'Africa/Brazzaville',
  'Africa/Bujumbura',
  'Africa/Cairo',
  'Africa/Casablanca',
  'Africa/Ceuta',
  'Africa/Conakry',
  'Africa/Dakar',
  'Africa/Dar_es_Salaam',
  'Africa/Djibouti',
  'Africa/Douala',
  'Africa/El_Aaiun',
  'Africa/Freetown',
  'Africa/Gaborone',
  'Africa/Harare',
  'Africa/Johannesburg',
  'Africa/Juba',
  'Africa/Kampala',
  'Africa/Khartoum',
  'Africa/Kigali',
  'Africa/Kinshasa',
  'Africa/Lagos',
  'Africa/Libreville',
  'Africa/Lome',
  'Africa/Luanda',
  'Africa/Lubumbashi',
  'Africa/Lusaka',
  'Africa/Malabo',
  'Africa/Maputo',
  'Africa/Maseru',
  'Africa/Mbabane',
  'Africa/Mogadishu',
  'Africa/Monrovia',
  'Africa/Nairobi',
  'Africa/Ndjamena',
  'Africa/Niamey',
  'Africa/Nouakchott',
  'Africa/Ouagadougou',
  'Africa/Porto-Novo',
  'Africa/Sao_Tome',
  'Africa/Tripoli',
  'Africa/Tunis',
  'Africa/Windhoek',

  // Americas
  'America/Adak',
  'America/Anchorage',
  'America/Anguilla',
  'America/Antigua',
  'America/Araguaina',
  'America/Argentina/Buenos_Aires',
  'America/Argentina/Catamarca',
  'America/Argentina/Cordoba',
  'America/Argentina/Jujuy',
  'America/Argentina/La_Rioja',
  'America/Argentina/Mendoza',
  'America/Argentina/Rio_Gallegos',
  'America/Argentina/Salta',
  'America/Argentina/San_Juan',
  'America/Argentina/San_Luis',
  'America/Argentina/Tucuman',
  'America/Argentina/Ushuaia',
  'America/Aruba',
  'America/Asuncion',
  'America/Atikokan',
  'America/Bahia',
  'America/Bahia_Banderas',
  'America/Barbados',
  'America/Belem',
  'America/Belize',
  'America/Blanc-Sablon',
  'America/Boa_Vista',
  'America/Bogota',
  'America/Boise',
  'America/Cambridge_Bay',
  'America/Campo_Grande',
  'America/Cancun',
  'America/Caracas',
  'America/Cayenne',
  'America/Cayman',
  'America/Chicago',
  'America/Chihuahua',
  'America/Costa_Rica',
  'America/Creston',
  'America/Cuiaba',
  'America/Curacao',
  'America/Danmarkshavn',
  'America/Dawson',
  'America/Dawson_Creek',
  'America/Denver',
  'America/Detroit',
  'America/Dominica',
  'America/Edmonton',
  'America/Eirunepe',
  'America/El_Salvador',
  'America/Fort_Nelson',
  'America/Fortaleza',
  'America/Glace_Bay',
  'America/Godthab',
  'America/Goose_Bay',
  'America/Grand_Turk',
  'America/Grenada',
  'America/Guadeloupe',
  'America/Guatemala',
  'America/Guayaquil',
  'America/Guyana',
  'America/Halifax',
  'America/Havana',
  'America/Hermosillo',
  'America/Indiana/Indianapolis',
  'America/Indiana/Knox',
  'America/Indiana/Marengo',
  'America/Indiana/Petersburg',
  'America/Indiana/Tell_City',
  'America/Indiana/Vevay',
  'America/Indiana/Vincennes',
  'America/Indiana/Winamac',
  'America/Inuvik',
  'America/Iqaluit',
  'America/Jamaica',
  'America/Juneau',
  'America/Kentucky/Louisville',
  'America/Kentucky/Monticello',
  'America/Kralendijk',
  'America/La_Paz',
  'America/Lima',
  'America/Los_Angeles',
  'America/Lower_Princes',
  'America/Maceio',
  'America/Managua',
  'America/Manaus',
  'America/Marigot',
  'America/Martinique',
  'America/Matamoros',
  'America/Mazatlan',
  'America/Menominee',
  'America/Merida',
  'America/Metlakatla',
  'America/Mexico_City',
  'America/Miquelon',
  'America/Moncton',
  'America/Monterrey',
  'America/Montevideo',
  'America/Montserrat',
  'America/Nassau',
  'America/New_York',
  'America/Nipigon',
  'America/Nome',
  'America/Noronha',
  'America/North_Dakota/Beulah',
  'America/North_Dakota/Center',
  'America/North_Dakota/New_Salem',
  'America/Ojinaga',
  'America/Panama',
  'America/Pangnirtung',
  'America/Paramaribo',
  'America/Phoenix',
  'America/Port-au-Prince',
  'America/Port_of_Spain',
  'America/Porto_Velho',
  'America/Puerto_Rico',
  'America/Punta_Arenas',
  'America/Rainy_River',
  'America/Rankin_Inlet',
  'America/Recife',
  'America/Regina',
  'America/Resolute',
  'America/Rio_Branco',
  'America/Santarem',
  'America/Santiago',
  'America/Santo_Domingo',
  'America/Sao_Paulo',
  'America/Scoresbysund',
  'America/Sitka',
  'America/St_Barthelemy',
  'America/St_Johns',
  'America/St_Kitts',
  'America/St_Lucia',
  'America/St_Thomas',
  'America/St_Vincent',
  'America/Swift_Current',
  'America/Tegucigalpa',
  'America/Thule',
  'America/Thunder_Bay',
  'America/Tijuana',
  'America/Toronto',
  'America/Tortola',
  'America/Vancouver',
  'America/Whitehorse',
  'America/Winnipeg',
  'America/Yakutat',
  'America/Yellowknife',

  // Antarctica
  'Antarctica/Casey',
  'Antarctica/Davis',
  'Antarctica/DumontDUrville',
  'Antarctica/Macquarie',
  'Antarctica/Mawson',
  'Antarctica/McMurdo',
  'Antarctica/Palmer',
  'Antarctica/Rothera',
  'Antarctica/Syowa',
  'Antarctica/Troll',
  'Antarctica/Vostok',

  // Arctic
  'Arctic/Longyearbyen',

  // Asia
  'Asia/Aden',
  'Asia/Almaty',
  'Asia/Amman',
  'Asia/Anadyr',
  'Asia/Aqtau',
  'Asia/Aqtobe',
  'Asia/Ashgabat',
  'Asia/Atyrau',
  'Asia/Baghdad',
  'Asia/Bahrain',
  'Asia/Baku',
  'Asia/Bangkok',
  'Asia/Barnaul',
  'Asia/Beirut',
  'Asia/Bishkek',
  'Asia/Brunei',
  'Asia/Chita',
  'Asia/Choibalsan',
  'Asia/Colombo',
  'Asia/Damascus',
  'Asia/Dhaka',
  'Asia/Dili',
  'Asia/Dubai',
  'Asia/Dushanbe',
  'Asia/Famagusta',
  'Asia/Gaza',
  'Asia/Hebron',
  'Asia/Ho_Chi_Minh',
  'Asia/Hong_Kong',
  'Asia/Hovd',
  'Asia/Irkutsk',
  'Asia/Jakarta',
  'Asia/Jayapura',
  'Asia/Jerusalem',
  'Asia/Kabul',
  'Asia/Kamchatka',
  'Asia/Karachi',
  'Asia/Kathmandu',
  'Asia/Khandyga',
  'Asia/Kolkata',
  'Asia/Krasnoyarsk',
  'Asia/Kuala_Lumpur',
  'Asia/Kuching',
  'Asia/Kuwait',
  'Asia/Macau',
  'Asia/Magadan',
  'Asia/Makassar',
  'Asia/Manila',
  'Asia/Muscat',
  'Asia/Nicosia',
  'Asia/Novokuznetsk',
  'Asia/Novosibirsk',
  'Asia/Omsk',
  'Asia/Oral',
  'Asia/Phnom_Penh',
  'Asia/Pontianak',
  'Asia/Pyongyang',
  'Asia/Qatar',
  'Asia/Qostanay',
  'Asia/Qyzylorda',
  'Asia/Riyadh',
  'Asia/Sakhalin',
  'Asia/Samarkand',
  'Asia/Seoul',
  'Asia/Shanghai',
  'Asia/Singapore',
  'Asia/Srednekolymsk',
  'Asia/Taipei',
  'Asia/Tashkent',
  'Asia/Tbilisi',
  'Asia/Tehran',
  'Asia/Thimphu',
  'Asia/Tokyo',
  'Asia/Tomsk',
  'Asia/Ulaanbaatar',
  'Asia/Urumqi',
  'Asia/Ust-Nera',
  'Asia/Vientiane',
  'Asia/Vladivostok',
  'Asia/Yakutsk',
  'Asia/Yangon',
  'Asia/Yekaterinburg',
  'Asia/Yerevan',

  // Atlantic
  'Atlantic/Azores',
  'Atlantic/Bermuda',
  'Atlantic/Canary',
  'Atlantic/Cape_Verde',
  'Atlantic/Faroe',
  'Atlantic/Madeira',
  'Atlantic/Reykjavik',
  'Atlantic/South_Georgia',
  'Atlantic/St_Helena',
  'Atlantic/Stanley',

  // Australia
  'Australia/Adelaide',
  'Australia/Brisbane',
  'Australia/Broken_Hill',
  'Australia/Currie',
  'Australia/Darwin',
  'Australia/Eucla',
  'Australia/Hobart',
  'Australia/Lindeman',
  'Australia/Lord_Howe',
  'Australia/Melbourne',
  'Australia/Perth',
  'Australia/Sydney',

  // Europe
  'Europe/Amsterdam',
  'Europe/Andorra',
  'Europe/Astrakhan',
  'Europe/Athens',
  'Europe/Belgrade',
  'Europe/Berlin',
  'Europe/Bratislava',
  'Europe/Brussels',
  'Europe/Bucharest',
  'Europe/Budapest',
  'Europe/Busingen',
  'Europe/Chisinau',
  'Europe/Copenhagen',
  'Europe/Dublin',
  'Europe/Gibraltar',
  'Europe/Guernsey',
  'Europe/Helsinki',
  'Europe/Isle_of_Man',
  'Europe/Istanbul',
  'Europe/Jersey',
  'Europe/Kaliningrad',
  'Europe/Kiev',
  'Europe/Kirov',
  'Europe/Lisbon',
  'Europe/Ljubljana',
  'Europe/London',
  'Europe/Luxembourg',
  'Europe/Madrid',
  'Europe/Malta',
  'Europe/Mariehamn',
  'Europe/Minsk',
  'Europe/Monaco',
  'Europe/Moscow',
  'Europe/Oslo',
  'Europe/Paris',
  'Europe/Podgorica',
  'Europe/Prague',
  'Europe/Riga',
  'Europe/Rome',
  'Europe/Samara',
  'Europe/San_Marino',
  'Europe/Sarajevo',
  'Europe/Saratov',
  'Europe/Simferopol',
  'Europe/Skopje',
  'Europe/Sofia',
  'Europe/Stockholm',
  'Europe/Tallinn',
  'Europe/Tirane',
  'Europe/Ulyanovsk',
  'Europe/Uzhgorod',
  'Europe/Vaduz',
  'Europe/Vatican',
  'Europe/Vienna',
  'Europe/Vilnius',
  'Europe/Volgograd',
  'Europe/Warsaw',
  'Europe/Zagreb',
  'Europe/Zaporozhye',
  'Europe/Zurich',

  // Indian
  'Indian/Antananarivo',
  'Indian/Chagos',
  'Indian/Christmas',
  'Indian/Cocos',
  'Indian/Comoro',
  'Indian/Kerguelen',
  'Indian/Mahe',
  'Indian/Maldives',
  'Indian/Mauritius',
  'Indian/Mayotte',
  'Indian/Reunion',

  // Pacific
  'Pacific/Apia',
  'Pacific/Auckland',
  'Pacific/Bougainville',
  'Pacific/Chatham',
  'Pacific/Chuuk',
  'Pacific/Easter',
  'Pacific/Efate',
  'Pacific/Enderbury',
  'Pacific/Fakaofo',
  'Pacific/Fiji',
  'Pacific/Funafuti',
  'Pacific/Galapagos',
  'Pacific/Gambier',
  'Pacific/Guadalcanal',
  'Pacific/Guam',
  'Pacific/Honolulu',
  'Pacific/Johnston',
  'Pacific/Kiritimati',
  'Pacific/Kosrae',
  'Pacific/Kwajalein',
  'Pacific/Majuro',
  'Pacific/Marquesas',
  'Pacific/Midway',
  'Pacific/Nauru',
  'Pacific/Niue',
  'Pacific/Norfolk',
  'Pacific/Noumea',
  'Pacific/Pago_Pago',
  'Pacific/Palau',
  'Pacific/Pitcairn',
  'Pacific/Pohnpei',
  'Pacific/Port_Moresby',
  'Pacific/Rarotonga',
  'Pacific/Saipan',
  'Pacific/Tahiti',
  'Pacific/Tarawa',
  'Pacific/Tongatapu',
  'Pacific/Wake',
  'Pacific/Wallis',

  // UTC
  'UTC',
];

export default function LocationForm({ location, onClose, onSuccess }: LocationFormProps) {
  const isEditing = !!location;
  const createLocationMutation = useCreateLocation();
  const updateLocationMutation = useUpdateLocation();
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
    control,
  } = useForm<LocationFormData>({
    resolver: zodResolver(locationSchema),
    defaultValues: {
      name: '',
      shortName: '',
      address: '',
      city: '',
      mobile: '',
      isMobileHidden: false,
      fax: '',
      floor: '',
      parking: false,
      elevator: false,
      handicapAccess: false,
      timezone: '',
      country: 'Algeria',
      postalCode: '',
      latitude: 0,
      longitude: 0,
      openingHours: [],
    },
  });

  // Field array for opening hours
  const { fields: openingHoursFields, append: appendOpeningHour, remove: removeOpeningHour } = useFieldArray({
    control,
    name: 'openingHours',
  });

  // Auto-fill timezone with user's timezone
  const handleAutoFillTimezone = () => {
    const userTimezone = getUserTimezone();
    setValue('timezone', userTimezone);
  };

  // Auto-fill location coordinates
  const handleAutoFillLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.');
      return;
    }

    setIsGettingLocation(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setValue('latitude', latitude);
        setValue('longitude', longitude);
        setIsGettingLocation(false);
      },
      (error) => {
        console.error('Error getting location:', error);
        let errorMessage = 'Unable to get your location. ';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage += 'Please allow location access and try again.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage += 'Location information is unavailable.';
            break;
          case error.TIMEOUT:
            errorMessage += 'Location request timed out.';
            break;
          default:
            errorMessage += 'An unknown error occurred.';
            break;
        }
        alert(errorMessage);
        setIsGettingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    );
  };

  // Helper functions for opening hours
  const addOpeningHour = () => {
    const existingDays = openingHoursFields.map(field => watch(`openingHours.${openingHoursFields.indexOf(field)}.dayOfWeek`));
    const availableDays = DAYS_OF_WEEK.filter(day => !existingDays.includes(day));
    const nextDay = availableDays.length > 0 ? availableDays[0] : 'Monday';

    appendOpeningHour({
      dayOfWeek: nextDay,
      isActive: true,
      hours: [{ timeFrom: '09:00', timeTo: '17:00' }],
    });
  };

  const addTimeSlot = (dayIndex: number) => {
    const currentHours = watch(`openingHours.${dayIndex}.hours`) || [];
    setValue(`openingHours.${dayIndex}.hours`, [
      ...currentHours,
      { timeFrom: '09:00', timeTo: '17:00' }
    ]);
  };

  const removeTimeSlot = (dayIndex: number, hourIndex: number) => {
    const currentHours = watch(`openingHours.${dayIndex}.hours`) || [];
    const newHours = currentHours.filter((_, index) => index !== hourIndex);
    setValue(`openingHours.${dayIndex}.hours`, newHours);
  };

  const setupStandardHours = () => {
    const standardHours = [
      { dayOfWeek: 'Monday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Tuesday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Wednesday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Thursday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Friday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Saturday', isActive: false, hours: [] },
      { dayOfWeek: 'Sunday', isActive: false, hours: [] },
    ];
    setValue('openingHours', standardHours);
  };

  // Populate form when editing or auto-fill timezone for new locations
  useEffect(() => {
    if (location) {
      reset({
        name: location.name,
        shortName: location.shortName || '',
        address: location.address || '',
        city: location.city || '',
        mobile: location.mobile || '',
        isMobileHidden: location.isMobileHidden,
        fax: location.fax || '',
        floor: location.floor || '',
        parking: location.parking,
        elevator: location.elevator,
        handicapAccess: location.handicapAccess,
        timezone: location.timezone || '',
        country: location.country || 'Algeria',
        postalCode: location.postalCode || '',
        latitude: location.latitude,
        longitude: location.longitude,
        openingHours: location.openingHours?.map(opening => ({
          dayOfWeek: opening.dayOfWeek,
          isActive: opening.isActive,
          hours: opening.hours?.map(hour => ({
            timeFrom: hour.timeFrom,
            timeTo: hour.timeTo,
          })) || [],
        })) || [],
      });
    } else {
      // Auto-fill timezone for new locations
      const userTimezone = getUserTimezone();
      setValue('timezone', userTimezone);
    }
  }, [location, reset, setValue]);

  const onSubmit = async (data: LocationFormData) => {
    try {
      const locationData: LocationCreateRequest = {
        name: data.name,
        shortName: data.shortName || undefined,
        address: data.address || undefined,
        city: data.city || undefined,
        mobile: data.mobile,
        isMobileHidden: data.isMobileHidden,
        fax: data.fax || undefined,
        floor: data.floor || undefined,
        parking: data.parking,
        elevator: data.elevator,
        handicapAccess: data.handicapAccess,
        timezone: data.timezone || undefined,
        // Flat address structure as per API
        country: data.country || 'Algeria',
        postalCode: data.postalCode || undefined,
        latitude: data.latitude,
        longitude: data.longitude,
        // Opening hours
        openingHours: data.openingHours && data.openingHours.length > 0 ? data.openingHours : undefined,
      };

      if (isEditing && location) {
        await updateLocationMutation.mutateAsync({
          id: location.id,
          data: locationData,
        });
      } else {
        await createLocationMutation.mutateAsync(locationData);
      }
      
      onSuccess();
    } catch (error) {
      // Error handled by mutations
    }
  };

  const isLoading = createLocationMutation.isPending || updateLocationMutation.isPending;
  const error = createLocationMutation.error || updateLocationMutation.error;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? 'Edit Location' : 'Add New Location'}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {isEditing ? 'Update location details' : 'Add a new service location'}
          </p>
        </div>
      </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-8">
            {/* Basic Information */}
            <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>
                    Location Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    {...register('name')}
                    placeholder="Enter location name"
                    disabled={isLoading}
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Short Name</Label>
                  <Input
                    {...register('shortName')}
                    placeholder="Optional short name"
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Address Information */}
            <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Address Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <Label>Street Address</Label>
                  <Input
                    {...register('address')}
                    placeholder="Enter street address"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>City</Label>
                  <Input
                    {...register('city')}
                    placeholder="Enter city"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>Country</Label>
                  <Input
                    {...register('country')}
                    placeholder="Enter country"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>Postal Code</Label>
                  <Input
                    {...register('postalCode')}
                    placeholder="Enter postal code"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>Floor/Suite</Label>
                  <Input
                    {...register('floor')}
                    placeholder="Floor or suite number"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label>Timezone</Label>
                    <button
                      type="button"
                      onClick={handleAutoFillTimezone}
                      disabled={isLoading}
                      className="text-xs text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300 font-medium"
                    >
                      Use my timezone
                    </button>
                  </div>
                  <select
                    {...register('timezone')}
                    disabled={isLoading}
                    className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                  >
                    <option value="">Select timezone</option>
                    {timezones.map((tz) => (
                      <option key={tz} value={tz}>
                        {tz}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Contact Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>
                    Mobile Phone <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    {...register('mobile')}
                    placeholder="Enter mobile number"
                    disabled={isLoading}
                  />
                  {errors.mobile && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.mobile.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Fax</Label>
                  <Input
                    {...register('fax')}
                    placeholder="Enter fax number"
                    disabled={isLoading}
                  />
                </div>

                <div className="md:col-span-2">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={watch('isMobileHidden')}
                      onChange={(checked) => setValue('isMobileHidden', checked)}
                      disabled={isLoading}
                    />
                    <div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Hide mobile number from customers
                      </span>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Mobile number will not be displayed publicly
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Amenities */}
            <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Amenities & Accessibility
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('parking')}
                    onChange={(checked) => setValue('parking', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Parking Available
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      On-site parking for customers
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('elevator')}
                    onChange={(checked) => setValue('elevator', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Elevator Access
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Elevator available in building
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('handicapAccess')}
                    onChange={(checked) => setValue('handicapAccess', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Handicap Accessible
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Wheelchair accessible entrance
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Coordinates */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Coordinates
              </h3>
              <div className="flex flex-col md:flex-row gap-6 items-end">
                <div className="flex-1">
                  <Label>
                    Latitude <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    {...register('latitude', { valueAsNumber: true })}
                    type="number"
                    step="any"
                    placeholder="e.g., 40.7128"
                    disabled={isLoading}
                  />
                  {errors.latitude && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.latitude.message}
                    </p>
                  )}
                </div>

                <div className="flex-1">
                  <Label>
                    Longitude <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    {...register('longitude', { valueAsNumber: true })}
                    type="number"
                    step="any"
                    placeholder="e.g., -74.0060"
                    disabled={isLoading}
                  />
                  {errors.longitude && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.longitude.message}
                    </p>
                  )}
                </div>

                <div className="flex-shrink-0">
                  <Button
                    type="button"
                    size="sm"
                    onClick={handleAutoFillLocation}
                    disabled={isLoading || isGettingLocation}
                    className="w-11 h-11 bg-brand-600 hover:bg-brand-700 text-white flex items-center justify-center rounded-lg"
                    title={isGettingLocation ? 'Getting location...' : 'Use my current location'}
                  >
                    <svg
                      className="w-6 h-6"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                    >
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                      <circle cx="12" cy="10" r="3"/>
                    </svg>
                  </Button>
                </div>
              </div>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Coordinates help customers find your location more easily on maps
              </p>
            </div>

            {/* Opening Hours */}
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  <TimeIcon className="inline-block w-5 h-5 mr-2" />
                  Opening Hours
                </h3>
                <div className="flex space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={setupStandardHours}
                    disabled={isLoading}
                  >
                    Standard Hours
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addOpeningHour}
                    disabled={isLoading}
                  >
                    <PlusIcon className="w-4 h-4 mr-2" />
                    Add Day
                  </Button>
                </div>
              </div>

              {openingHoursFields.length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <TimeIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No opening hours set. Click "Add Day" to get started.</p>
                </div>
              )}

              <div className="space-y-4">
                {openingHoursFields.map((field, dayIndex) => (
                  <div key={field.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div>
                          <Label>Day of Week</Label>
                          <select
                            {...register(`openingHours.${dayIndex}.dayOfWeek`)}
                            disabled={isLoading}
                            className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 shadow-sm focus:border-brand-300 focus:outline-none focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                          >
                            {DAYS_OF_WEEK.map((day) => (
                              <option key={day} value={day}>
                                {day}
                              </option>
                            ))}
                          </select>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={watch(`openingHours.${dayIndex}.isActive`)}
                            onChange={(checked) => setValue(`openingHours.${dayIndex}.isActive`, checked)}
                            disabled={isLoading}
                          />
                          <Label>Active</Label>
                        </div>
                      </div>

                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeOpeningHour(dayIndex)}
                        disabled={isLoading}
                        className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                      >
                        <TrashBinIcon className="w-4 h-4" />
                      </Button>
                    </div>

                    {watch(`openingHours.${dayIndex}.isActive`) && (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label>Time Slots</Label>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => addTimeSlot(dayIndex)}
                            disabled={isLoading}
                          >
                            <PlusIcon className="w-4 h-4 mr-1" />
                            Add Slot
                          </Button>
                        </div>

                        {watch(`openingHours.${dayIndex}.hours`)?.map((_, hourIndex) => (
                          <div key={hourIndex} className="flex items-center space-x-3">
                            <div className="flex-1">
                              <Label>From</Label>
                              <Input
                                {...register(`openingHours.${dayIndex}.hours.${hourIndex}.timeFrom`)}
                                type="time"
                                disabled={isLoading}
                              />
                            </div>
                            <div className="flex-1">
                              <Label>To</Label>
                              <Input
                                {...register(`openingHours.${dayIndex}.hours.${hourIndex}.timeTo`)}
                                type="time"
                                disabled={isLoading}
                              />
                            </div>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeTimeSlot(dayIndex, hourIndex)}
                              disabled={isLoading}
                              className="mt-6 text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                            >
                              <TrashBinIcon className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}

                        {(!watch(`openingHours.${dayIndex}.hours`) || watch(`openingHours.${dayIndex}.hours`)?.length === 0) && (
                          <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-2">
                            No time slots. Click "Add Slot" to add opening hours.
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {error && (
              <ErrorDisplay
                error={error}
                variant="banner"
                size="sm"
              />
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
              >
                {isLoading 
                  ? (isEditing ? 'Updating...' : 'Creating...') 
                  : (isEditing ? 'Update Location' : 'Create Location')
                }
              </Button>
            </div>
          </form>
    </div>
  );
}
