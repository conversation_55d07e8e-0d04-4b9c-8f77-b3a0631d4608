import React, { useEffect, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import Checkbox from '../form/input/Checkbox';
import { ErrorDisplay } from '../error';
import { useCreateLocation, useUpdateLocation } from '../../hooks/useLocations';
import { Location, LocationCreateRequest } from '../../types';
import { PlusIcon, TrashBinIcon, TimeIcon, BoltIcon } from '../../icons';
import { getUserTimezone } from '../../utils/timezone';

// Opening hours schema matching API
const openingHoursSchema = z.object({
  dayOfWeek: z.string().min(1, "Day of week is required"),
  isActive: z.boolean().default(true),
  hours: z.array(z.object({
    timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
    timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
  })),
});

// Validation schema
const locationSchema = z.object({
  name: z.string().min(1, "Location name is required"),
  shortName: z.string().max(50, 'Short name too long').optional(),
  address: z.string().max(500, 'Address too long').optional(),
  city: z.string().max(100, 'City name too long').optional(),
  mobile: z.string().min(1, "Mobile number is required"),
  isMobileHidden: z.boolean().default(false),
  fax: z.string().max(50, 'Fax too long').optional(),
  floor: z.string().max(50, 'Floor too long').optional(),
  parking: z.boolean().default(false),
  elevator: z.boolean().default(false),
  handicapAccess: z.boolean().default(false),
  timezone: z.string().max(50, 'Timezone too long').optional(),
  // Address details (flat structure as per API)
  country: z.string().max(100, 'Country name too long').default('Algeria'),
  postalCode: z.string().max(20, 'Postal code too long').optional(),
  latitude: z.number().min(-90).max(90).optional(),
  longitude: z.number().min(-180).max(180).optional(),
  // Opening hours
  openingHours: z.array(openingHoursSchema).optional(),
});

type LocationFormData = z.infer<typeof locationSchema>;

interface LocationFormProps {
  location?: Location | null;
  onClose: () => void;
  onSuccess: () => void;
}

// Days of the week
const DAYS_OF_WEEK = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
];

const timezones = [
  'America/New_York',
  'America/Chicago',
  'America/Denver',
  'America/Los_Angeles',
  'America/Phoenix',
  'America/Anchorage',
  'Pacific/Honolulu',
  'Europe/London',
  'Europe/Paris',
  'Europe/Berlin',
  'Asia/Tokyo',
  'Asia/Shanghai',
  'Australia/Sydney',
];

export default function LocationForm({ location, onClose, onSuccess }: LocationFormProps) {
  const isEditing = !!location;
  const createLocationMutation = useCreateLocation();
  const updateLocationMutation = useUpdateLocation();
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
    control,
  } = useForm<LocationFormData>({
    resolver: zodResolver(locationSchema),
    defaultValues: {
      name: '',
      shortName: '',
      address: '',
      city: '',
      mobile: '',
      isMobileHidden: false,
      fax: '',
      floor: '',
      parking: false,
      elevator: false,
      handicapAccess: false,
      timezone: '',
      country: 'Algeria',
      postalCode: '',
      latitude: undefined,
      longitude: undefined,
      openingHours: [],
    },
  });

  // Field array for opening hours
  const { fields: openingHoursFields, append: appendOpeningHour, remove: removeOpeningHour } = useFieldArray({
    control,
    name: 'openingHours',
  });

  // Auto-fill timezone with user's timezone
  const handleAutoFillTimezone = () => {
    const userTimezone = getUserTimezone();
    setValue('timezone', userTimezone);
  };

  // Auto-fill location coordinates
  const handleAutoFillLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.');
      return;
    }

    setIsGettingLocation(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setValue('latitude', latitude);
        setValue('longitude', longitude);
        setIsGettingLocation(false);
      },
      (error) => {
        console.error('Error getting location:', error);
        let errorMessage = 'Unable to get your location. ';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage += 'Please allow location access and try again.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage += 'Location information is unavailable.';
            break;
          case error.TIMEOUT:
            errorMessage += 'Location request timed out.';
            break;
          default:
            errorMessage += 'An unknown error occurred.';
            break;
        }
        alert(errorMessage);
        setIsGettingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    );
  };

  // Helper functions for opening hours
  const addOpeningHour = () => {
    const existingDays = openingHoursFields.map(field => watch(`openingHours.${openingHoursFields.indexOf(field)}.dayOfWeek`));
    const availableDays = DAYS_OF_WEEK.filter(day => !existingDays.includes(day));
    const nextDay = availableDays.length > 0 ? availableDays[0] : 'Monday';

    appendOpeningHour({
      dayOfWeek: nextDay,
      isActive: true,
      hours: [{ timeFrom: '09:00', timeTo: '17:00' }],
    });
  };

  const addTimeSlot = (dayIndex: number) => {
    const currentHours = watch(`openingHours.${dayIndex}.hours`) || [];
    setValue(`openingHours.${dayIndex}.hours`, [
      ...currentHours,
      { timeFrom: '09:00', timeTo: '17:00' }
    ]);
  };

  const removeTimeSlot = (dayIndex: number, hourIndex: number) => {
    const currentHours = watch(`openingHours.${dayIndex}.hours`) || [];
    const newHours = currentHours.filter((_, index) => index !== hourIndex);
    setValue(`openingHours.${dayIndex}.hours`, newHours);
  };

  const setupStandardHours = () => {
    const standardHours = [
      { dayOfWeek: 'Monday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Tuesday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Wednesday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Thursday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Friday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Saturday', isActive: false, hours: [] },
      { dayOfWeek: 'Sunday', isActive: false, hours: [] },
    ];
    setValue('openingHours', standardHours);
  };

  // Populate form when editing or auto-fill timezone for new locations
  useEffect(() => {
    if (location) {
      reset({
        name: location.name,
        shortName: location.shortName || '',
        address: location.address || '',
        city: location.city || '',
        mobile: location.mobile || '',
        isMobileHidden: location.isMobileHidden,
        fax: location.fax || '',
        floor: location.floor || '',
        parking: location.parking,
        elevator: location.elevator,
        handicapAccess: location.handicapAccess,
        timezone: location.timezone || '',
        country: location.country || 'Algeria',
        postalCode: location.postalCode || '',
        latitude: location.latitude,
        longitude: location.longitude,
        openingHours: location.openingHours?.map(opening => ({
          dayOfWeek: opening.dayOfWeek,
          isActive: opening.isActive,
          hours: opening.hours?.map(hour => ({
            timeFrom: hour.timeFrom,
            timeTo: hour.timeTo,
          })) || [],
        })) || [],
      });
    } else {
      // Auto-fill timezone for new locations
      const userTimezone = getUserTimezone();
      setValue('timezone', userTimezone);
    }
  }, [location, reset, setValue]);

  const onSubmit = async (data: LocationFormData) => {
    try {
      const locationData: LocationCreateRequest = {
        name: data.name,
        shortName: data.shortName || undefined,
        address: data.address || undefined,
        city: data.city || undefined,
        mobile: data.mobile,
        isMobileHidden: data.isMobileHidden,
        fax: data.fax || undefined,
        floor: data.floor || undefined,
        parking: data.parking,
        elevator: data.elevator,
        handicapAccess: data.handicapAccess,
        timezone: data.timezone || undefined,
        // Flat address structure as per API
        country: data.country || 'Algeria',
        postalCode: data.postalCode || undefined,
        latitude: data.latitude || undefined,
        longitude: data.longitude || undefined,
        // Opening hours
        openingHours: data.openingHours && data.openingHours.length > 0 ? data.openingHours : undefined,
      };

      if (isEditing && location) {
        await updateLocationMutation.mutateAsync({
          id: location.id,
          data: locationData,
        });
      } else {
        await createLocationMutation.mutateAsync(locationData);
      }
      
      onSuccess();
    } catch (error) {
      // Error handled by mutations
    }
  };

  const isLoading = createLocationMutation.isPending || updateLocationMutation.isPending;
  const error = createLocationMutation.error || updateLocationMutation.error;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? 'Edit Location' : 'Add New Location'}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {isEditing ? 'Update location details' : 'Add a new service location'}
          </p>
        </div>
      </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>
                    Location Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    {...register('name')}
                    placeholder="Enter location name"
                    disabled={isLoading}
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Short Name</Label>
                  <Input
                    {...register('shortName')}
                    placeholder="Optional short name"
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Address Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Address Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <Label>Street Address</Label>
                  <Input
                    {...register('address')}
                    placeholder="Enter street address"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>City</Label>
                  <Input
                    {...register('city')}
                    placeholder="Enter city"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>Country</Label>
                  <Input
                    {...register('country')}
                    placeholder="Enter country"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>Postal Code</Label>
                  <Input
                    {...register('postalCode')}
                    placeholder="Enter postal code"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>Floor/Suite</Label>
                  <Input
                    {...register('floor')}
                    placeholder="Floor or suite number"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label>Timezone</Label>
                    <button
                      type="button"
                      onClick={handleAutoFillTimezone}
                      disabled={isLoading}
                      className="text-xs text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300 font-medium"
                    >
                      Use my timezone
                    </button>
                  </div>
                  <select
                    {...register('timezone')}
                    disabled={isLoading}
                    className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                  >
                    <option value="">Select timezone</option>
                    {timezones.map((tz) => (
                      <option key={tz} value={tz}>
                        {tz}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Contact Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>
                    Mobile Phone <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    {...register('mobile')}
                    placeholder="Enter mobile number"
                    disabled={isLoading}
                  />
                  {errors.mobile && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.mobile.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Fax</Label>
                  <Input
                    {...register('fax')}
                    placeholder="Enter fax number"
                    disabled={isLoading}
                  />
                </div>

                <div className="md:col-span-2">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={watch('isMobileHidden')}
                      onChange={(checked) => setValue('isMobileHidden', checked)}
                      disabled={isLoading}
                    />
                    <div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Hide mobile number from customers
                      </span>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Mobile number will not be displayed publicly
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Amenities */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Amenities & Accessibility
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('parking')}
                    onChange={(checked) => setValue('parking', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Parking Available
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      On-site parking for customers
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('elevator')}
                    onChange={(checked) => setValue('elevator', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Elevator Access
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Elevator available in building
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('handicapAccess')}
                    onChange={(checked) => setValue('handicapAccess', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Handicap Accessible
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Wheelchair accessible entrance
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Coordinates (Optional) */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Coordinates (Optional)
                </h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAutoFillLocation}
                  disabled={isLoading || isGettingLocation}
                  className="flex items-center gap-2"
                >
                  <BoltIcon className="w-4 h-4" />
                  {isGettingLocation ? 'Getting location...' : 'Use my location'}
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>Latitude</Label>
                  <Input
                    {...register('latitude', { valueAsNumber: true })}
                    type="number"
                    step="any"
                    placeholder="e.g., 40.7128"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label>Longitude</Label>
                  <Input
                    {...register('longitude', { valueAsNumber: true })}
                    type="number"
                    step="any"
                    placeholder="e.g., -74.0060"
                    disabled={isLoading}
                  />
                </div>
              </div>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Coordinates help customers find your location more easily on maps
              </p>
            </div>

            {/* Opening Hours */}
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  <TimeIcon className="inline-block w-5 h-5 mr-2" />
                  Opening Hours
                </h3>
                <div className="flex space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={setupStandardHours}
                    disabled={isLoading}
                  >
                    Standard Hours
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addOpeningHour}
                    disabled={isLoading}
                  >
                    <PlusIcon className="w-4 h-4 mr-2" />
                    Add Day
                  </Button>
                </div>
              </div>

              {openingHoursFields.length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <TimeIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No opening hours set. Click "Add Day" to get started.</p>
                </div>
              )}

              <div className="space-y-4">
                {openingHoursFields.map((field, dayIndex) => (
                  <div key={field.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div>
                          <Label>Day of Week</Label>
                          <select
                            {...register(`openingHours.${dayIndex}.dayOfWeek`)}
                            disabled={isLoading}
                            className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 shadow-sm focus:border-brand-300 focus:outline-none focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                          >
                            {DAYS_OF_WEEK.map((day) => (
                              <option key={day} value={day}>
                                {day}
                              </option>
                            ))}
                          </select>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={watch(`openingHours.${dayIndex}.isActive`)}
                            onChange={(checked) => setValue(`openingHours.${dayIndex}.isActive`, checked)}
                            disabled={isLoading}
                          />
                          <Label>Active</Label>
                        </div>
                      </div>

                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeOpeningHour(dayIndex)}
                        disabled={isLoading}
                        className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                      >
                        <TrashBinIcon className="w-4 h-4" />
                      </Button>
                    </div>

                    {watch(`openingHours.${dayIndex}.isActive`) && (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label>Time Slots</Label>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => addTimeSlot(dayIndex)}
                            disabled={isLoading}
                          >
                            <PlusIcon className="w-4 h-4 mr-1" />
                            Add Slot
                          </Button>
                        </div>

                        {watch(`openingHours.${dayIndex}.hours`)?.map((_, hourIndex) => (
                          <div key={hourIndex} className="flex items-center space-x-3">
                            <div className="flex-1">
                              <Label>From</Label>
                              <Input
                                {...register(`openingHours.${dayIndex}.hours.${hourIndex}.timeFrom`)}
                                type="time"
                                disabled={isLoading}
                              />
                            </div>
                            <div className="flex-1">
                              <Label>To</Label>
                              <Input
                                {...register(`openingHours.${dayIndex}.hours.${hourIndex}.timeTo`)}
                                type="time"
                                disabled={isLoading}
                              />
                            </div>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeTimeSlot(dayIndex, hourIndex)}
                              disabled={isLoading}
                              className="mt-6 text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                            >
                              <TrashBinIcon className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}

                        {(!watch(`openingHours.${dayIndex}.hours`) || watch(`openingHours.${dayIndex}.hours`)?.length === 0) && (
                          <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-2">
                            No time slots. Click "Add Slot" to add opening hours.
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {error && (
              <ErrorDisplay
                error={error}
                variant="banner"
                size="sm"
              />
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
              >
                {isLoading 
                  ? (isEditing ? 'Updating...' : 'Creating...') 
                  : (isEditing ? 'Update Location' : 'Create Location')
                }
              </Button>
            </div>
          </form>
    </div>
  );
}
