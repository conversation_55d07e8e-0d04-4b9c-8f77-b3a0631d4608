import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { Modal } from "../../components/ui/modal";
import { ErrorDisplay } from "../../components/error";
import {
  useQueues,
  useQueueStats,
  useDeleteQueue,
  useQueueLimits,
  useCanCreateQueue
} from "../../hooks/useQueues";
import QueueCard from "../../components/queues/QueueCard";
import QueueForm from "../../components/queues/QueueForm";
import QueueDetails from "../../components/queues/QueueDetails";
import QueueStatsOverview from "../../components/queues/QueueStatsOverview";
import QueueLimitsCard from "../../components/queues/QueueLimitsCard";
import QueueRealTimeStatus from "../../components/queues/QueueRealTimeStatus";
import { Queue, QueueFilters } from "../../types/queue";
import { PlusIcon, ListIcon } from "../../icons";

export default function QueueManagement() {
  const [showQueueForm, setShowQueueForm] = useState(false);
  const [editingQueue, setEditingQueue] = useState<Queue | null>(null);
  const [selectedQueue, setSelectedQueue] = useState<Queue | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [filters, setFilters] = useState<QueueFilters>({});

  const { data: queues, isLoading, error } = useQueues(filters);
  const { data: queueStats } = useQueueStats();
  const { data: queueLimits } = useQueueLimits();
  const { data: canCreateQueue } = useCanCreateQueue();
  const deleteQueueMutation = useDeleteQueue();

  // Debug state changes (moved after variable declarations)
  React.useEffect(() => {
    console.log('🔄 showQueueForm state changed:', showQueueForm);
  }, [showQueueForm]);

  React.useEffect(() => {
    console.log('📊 canCreateQueue data loaded:', canCreateQueue);
  }, [canCreateQueue]);

  const handleCreateQueue = () => {
    console.log('🎯 Create Queue button clicked');
    console.log('📊 canCreateQueue data:', canCreateQueue);

    // Test with simple alert first
    alert('Create Queue button clicked! Check console for details.');

    // Temporarily disable limit check for debugging
    // if (canCreateQueue && !canCreateQueue.canCreate) {
    //   console.log('❌ Cannot create queue - limit reached');
    //   alert(canCreateQueue.message || 'Queue limit reached. Please upgrade your subscription.');
    //   return;
    // }

    console.log('✅ Opening queue form modal');
    setEditingQueue(null);
    setShowQueueForm(true);
  };

  const handleEditQueue = (queue: Queue) => {
    setEditingQueue(queue);
    setShowQueueForm(true);
  };

  const handleViewQueue = (queue: Queue) => {
    setSelectedQueue(queue);
    setShowDetails(true);
  };

  const handleDeleteQueue = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this queue? This action cannot be undone.')) {
      try {
        await deleteQueueMutation.mutateAsync(id);
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  const handleCloseForm = () => {
    setShowQueueForm(false);
    setEditingQueue(null);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedQueue(null);
  };

  const handleQueueSuccess = () => {
    handleCloseForm();
  };

  const handleFilterChange = (newFilters: Partial<QueueFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={error}
          title="Failed to load queues"
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title="Queue Management | Provider Dashboard"
        description="Manage customer queues, wait times, and queue-based appointments"
      />
      <PageBreadcrumb pageTitle="Queue Management" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Queue Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage customer queues and optimize wait times
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={(e) => {
                console.log('🖱️ Button click event triggered', e);
                handleCreateQueue();
              }}
              size="sm"
              disabled={false}
              startIcon={<PlusIcon className="w-4 h-4" />}
            >
              Create New Queue
            </Button>
          </div>
        </div>

        {/* Queue Statistics */}
        <div className="space-y-6">
          {queueStats && <QueueStatsOverview stats={queueStats} />}
        </div>

        {/* Queue Limits and Real-time Status */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            {queueLimits ? (
              <QueueLimitsCard limits={queueLimits} />
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              </div>
            )}
          </div>
          <div>
            {queues && queues.length > 0 && (
              <QueueRealTimeStatus queues={queues} />
            )}
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status
              </label>
              <select
                value={filters.isActive === undefined ? '' : filters.isActive ? 'active' : 'inactive'}
                onChange={(e) => {
                  const value = e.target.value;
                  handleFilterChange({ 
                    isActive: value === '' ? undefined : value === 'active'
                  });
                }}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Queues</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Location
              </label>
              <select
                value={filters.locationId || ''}
                onChange={(e) => handleFilterChange({ 
                  locationId: e.target.value ? Number(e.target.value) : undefined 
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Locations</option>
                {/* Add location options here */}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Service
              </label>
              <select
                value={filters.serviceId || ''}
                onChange={(e) => handleFilterChange({ 
                  serviceId: e.target.value ? Number(e.target.value) : undefined 
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Services</option>
                {/* Add service options here */}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Search
              </label>
              <input
                type="text"
                placeholder="Search queues..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange({ search: e.target.value || undefined })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
              />
            </div>
          </div>
        </div>

        {/* Queues List */}
        {queues && queues.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {queues.map((queue) => (
              <QueueCard
                key={queue.id}
                queue={queue}
                onView={() => handleViewQueue(queue)}
                onEdit={() => handleEditQueue(queue)}
                onDelete={() => handleDeleteQueue(queue.id)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No queues found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {Object.keys(filters).length > 0 
                ? "No queues match your current filters. Try adjusting your search criteria."
                : "Get started by creating your first queue to manage customer wait times."
              }
            </p>
            {Object.keys(filters).length === 0 && (
              <Button onClick={handleCreateQueue}>
                Create Your First Queue
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Queue Form Modal */}
      <Modal
        isOpen={showQueueForm}
        onClose={handleCloseForm}
        className="max-w-2xl p-0"
      >
        <QueueForm
          queue={editingQueue}
          onClose={handleCloseForm}
          onSuccess={handleQueueSuccess}
        />
      </Modal>

      {/* Queue Details Modal */}
      <Modal
        isOpen={showDetails && !!selectedQueue}
        onClose={handleCloseDetails}
        className="max-w-4xl p-0"
      >
        {selectedQueue && (
          <QueueDetails
            queue={selectedQueue}
            onClose={handleCloseDetails}
            onEdit={() => {
              setEditingQueue(selectedQueue);
              setShowDetails(false);
              setShowQueueForm(true);
            }}
          />
        )}
      </Modal>
    </>
  );
}
