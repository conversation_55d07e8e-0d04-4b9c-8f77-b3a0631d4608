import { useState, useRef, useMemo, useCallback, useEffect } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import { EventInput, DateSelectArg, EventClickArg } from "@fullcalendar/core";
import { Modal } from "../components/ui/modal";
import { useModal } from "../hooks/useModal";
import PageMeta from "../components/common/PageMeta";
import { useAppointments } from "../hooks/useAppointments";
import { Appointment } from "../types";
import AppointmentForm from "../components/appointments/AppointmentForm";
import AppointmentDetails from "../components/appointments/AppointmentDetails";
import { ErrorDisplay } from "../components/error";
import CalendarSidebar from "../components/calendar/CalendarSidebar";
import ServiceForm from "../components/services/ServiceForm";
import { formatLocalTime } from "../utils/timezone";

interface CalendarEvent extends EventInput {
  extendedProps: {
    appointmentId?: number;
    appointment?: Appointment;
    status: string;
    customerName?: string;
    serviceName?: string;
  };
}

const Calendar: React.FC = () => {
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'create' | 'view' | null>(null);
  const calendarRef = useRef<FullCalendar>(null);
  const { isOpen, openModal, closeModal } = useModal();

  // Service modal state
  const { isOpen: isServiceModalOpen, openModal: openServiceModal, closeModal: closeServiceModal } = useModal();

  // Sidebar state
  const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {
    const saved = localStorage.getItem('calendar-sidebar-collapsed');
    return saved ? JSON.parse(saved) : false;
  });

  // Calendar configuration state
  const [calendarConfig, setCalendarConfig] = useState(() => {
    try {
      const saved = localStorage.getItem('calendar-config');
      if (saved) {
        const parsed = JSON.parse(saved);
        // Validate the parsed config
        if (parsed &&
            typeof parsed.startTime === 'string' &&
            typeof parsed.endTime === 'string' &&
            typeof parsed.timeSlotInterval === 'number' &&
            parsed.startTime.match(/^\d{2}:\d{2}$/) &&
            parsed.endTime.match(/^\d{2}:\d{2}$/) &&
            parsed.timeSlotInterval > 0) {
          return parsed;
        }
      }
    } catch (error) {
      console.warn('Failed to parse calendar config from localStorage:', error);
    }

    // Return default config if parsing fails or data is invalid
    return {
      startTime: '08:00',
      endTime: '18:00',
      timeSlotInterval: 30,
    };
  });

  // Filter state
  const [selectedServices, setSelectedServices] = useState<number[]>(() => {
    const saved = localStorage.getItem('calendar-selected-services');
    return saved ? JSON.parse(saved) : [];
  });
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>(() => {
    const saved = localStorage.getItem('calendar-selected-statuses');
    return saved ? JSON.parse(saved) : ['pending', 'confirmed', 'completed', 'cancelled'];
  });

  // Fetch appointments data
  const { data: appointments, isLoading, error } = useAppointments();

  // Debug calendar config changes
  useEffect(() => {
    console.log('Calendar config updated:', calendarConfig);
    console.log('FullCalendar props will be:', {
      slotMinTime: calendarConfig.startTime || '08:00',
      slotMaxTime: calendarConfig.endTime || '18:00',
      slotDuration: `00:${calendarConfig.timeSlotInterval || 30}:00`
    });
  }, [calendarConfig]);

  // Filter appointments based on selected services and statuses
  const filteredAppointments = useMemo(() => {
    if (!appointments) return [];

    return appointments.filter(appointment => {
      // Filter by services
      const serviceMatch = selectedServices.length === 0 ||
        (appointment.service?.id && selectedServices.includes(appointment.service.id));

      // Filter by status
      const statusMatch = selectedStatuses.includes(appointment.status);

      return serviceMatch && statusMatch;
    });
  }, [appointments, selectedServices, selectedStatuses]);

  // Get color based on appointment status
  const getStatusColor = useCallback((status: string): string => {
    switch (status) {
      case 'pending':
        return '#F59E0B'; // yellow-500
      case 'confirmed':
        return '#10B981'; // green-500
      case 'completed':
        return '#3B82F6'; // blue-500
      case 'cancelled':
      case 'canceled':
        return '#EF4444'; // red-500
      case 'InProgress':
        return '#8B5CF6'; // purple
      case 'noshow':
      case 'no-show':
        return '#6B7280'; // gray
      default:
        return '#8B5CF6'; // purple
    }
  }, []);

  // Transform appointments to calendar events
  const events: CalendarEvent[] = useMemo(() => {
    if (!filteredAppointments) return [];

    return filteredAppointments.map((appointment) => {
      const startDateTime = new Date(appointment.expectedAppointmentStartTime);
      const endDateTime = new Date(appointment.expectedAppointmentEndTime);

      return {
        id: appointment.id.toString(),
        title: `${appointment.service?.title || 'Service'} - ${appointment.customer?.firstName} ${appointment.customer?.lastName}`,
        start: startDateTime.toISOString(),
        end: endDateTime.toISOString(),
        backgroundColor: getStatusColor(appointment.status),
        borderColor: getStatusColor(appointment.status),
        textColor: '#ffffff',
        extendedProps: {
          appointmentId: appointment.id,
          appointment,
          status: appointment.status,
          customerName: `${appointment.customer?.firstName} ${appointment.customer?.lastName}`,
          serviceName: appointment.service?.title,
        },
      };
    });
  }, [filteredAppointments, getStatusColor]);

  const handleDateSelect = (selectInfo: DateSelectArg) => {
    // Pass the selectInfo to get both date and time information
    setSelectedDate(selectInfo.startStr);
    setSelectedAppointment(null);
    setViewMode('create');
    openModal();
  };

  const handleEventClick = (clickInfo: EventClickArg) => {
    const appointment = clickInfo.event.extendedProps.appointment as Appointment;
    setSelectedAppointment(appointment);
    setSelectedDate(null);
    setViewMode('view');
    openModal();
  };

  const handleCreateAppointment = () => {
    setSelectedAppointment(null);
    setSelectedDate(null);
    setViewMode('create');
    openModal();
  };

  const handleCloseModal = () => {
    setSelectedAppointment(null);
    setSelectedDate(null);
    setViewMode(null);
    closeModal();
  };

  const handleAppointmentSuccess = () => {
    handleCloseModal();
  };

  // Sidebar handlers
  const handleSidebarToggle = () => {
    const newCollapsed = !sidebarCollapsed;
    setSidebarCollapsed(newCollapsed);
    localStorage.setItem('calendar-sidebar-collapsed', JSON.stringify(newCollapsed));
  };

  const handleConfigChange = (config: typeof calendarConfig) => {
    // Validate config values before setting
    const validatedConfig = {
      startTime: config.startTime || '08:00',
      endTime: config.endTime || '18:00',
      timeSlotInterval: config.timeSlotInterval || 30,
    };

    // Ensure end time is after start time
    if (validatedConfig.startTime >= validatedConfig.endTime) {
      console.warn('End time must be after start time. Adjusting end time.');
      const startHour = parseInt(validatedConfig.startTime.split(':')[0]);
      validatedConfig.endTime = `${String(startHour + 1).padStart(2, '0')}:00`;
    }

    console.log('Calendar config changing:', validatedConfig);
    setCalendarConfig(validatedConfig);
    localStorage.setItem('calendar-config', JSON.stringify(validatedConfig));

    // Update FullCalendar options dynamically
    setTimeout(() => {
      if (calendarRef.current) {
        const calendarApi = calendarRef.current.getApi();

        console.log('Current calendar view:', calendarApi.view.type);
        console.log('Before update - Current options:', {
          slotMinTime: calendarApi.getOption('slotMinTime'),
          slotMaxTime: calendarApi.getOption('slotMaxTime'),
          slotDuration: calendarApi.getOption('slotDuration')
        });

        // Update the slot times using setOption
        calendarApi.setOption('slotMinTime', validatedConfig.startTime);
        calendarApi.setOption('slotMaxTime', validatedConfig.endTime);
        calendarApi.setOption('slotDuration', `00:${validatedConfig.timeSlotInterval}:00`);

        console.log('After update - New options:', {
          slotMinTime: calendarApi.getOption('slotMinTime'),
          slotMaxTime: calendarApi.getOption('slotMaxTime'),
          slotDuration: calendarApi.getOption('slotDuration')
        });

        // Force a re-render of the current view
        calendarApi.render();
      }
    }, 100);
  };

  const handleServicesChange = (serviceIds: number[]) => {
    setSelectedServices(serviceIds);
    localStorage.setItem('calendar-selected-services', JSON.stringify(serviceIds));
  };

  const handleStatusesChange = (statuses: string[]) => {
    setSelectedStatuses(statuses);
    localStorage.setItem('calendar-selected-statuses', JSON.stringify(statuses));
  };

  const handleNewService = () => {
    openServiceModal();
  };

  const handleServiceSuccess = () => {
    closeServiceModal();
    // Optionally refresh services data or show success message
  };

  // Custom event content renderer
  const renderEventContent = (eventInfo: { event: { extendedProps: { appointment: Appointment } } }) => {
    const appointment = eventInfo.event.extendedProps.appointment as Appointment;

    return (
      <div className="p-1 text-xs">
        <div className="font-medium truncate">
          {appointment?.service?.title || 'Service'}
        </div>
        <div className="truncate opacity-90">
          {appointment?.customer?.firstName} {appointment?.customer?.lastName}
        </div>
        <div className="truncate opacity-75">
          {formatLocalTime(appointment?.expectedAppointmentStartTime || '')}
        </div>
      </div>
    );
  };

  if (error) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={error}
          title="Failed to load appointments"
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title="Appointment Calendar | Provider Dashboard"
        description="Manage your appointments and schedule with an interactive calendar"
      />
      <div className="flex h-[calc(100vh-120px)] rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03] overflow-hidden">
        {/* Calendar Sidebar */}
        <CalendarSidebar
          isCollapsed={sidebarCollapsed}
          onToggle={handleSidebarToggle}
          startTime={calendarConfig.startTime}
          endTime={calendarConfig.endTime}
          timeSlotInterval={calendarConfig.timeSlotInterval}
          onConfigChange={handleConfigChange}
          selectedServices={selectedServices}
          onServicesChange={handleServicesChange}
          onNewService={handleNewService}
          selectedStatuses={selectedStatuses}
          onStatusesChange={handleStatusesChange}
        />

        {/* Main Calendar Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Calendar Header */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                Appointment Calendar
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Manage your appointments and schedule
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {/* Status Legend */}
              <div className="flex items-center space-x-3 text-xs">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <span className="text-gray-600 dark:text-gray-400">Pending</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span className="text-gray-600 dark:text-gray-400">Confirmed</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                  <span className="text-gray-600 dark:text-gray-400">Completed</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <span className="text-gray-600 dark:text-gray-400">Cancelled</span>
                </div>
              </div>
            </div>
          </div>
          </div>

          <div className="custom-calendar p-6 flex-1 overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center h-96">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
            </div>
          ) : (
            <FullCalendar
              ref={calendarRef}
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              initialView="dayGridMonth"
              timeZone="local"
              headerToolbar={{
                left: "prev,next addAppointmentButton debugTimeSlots",
                center: "title",
                right: "dayGridMonth,timeGridWeek,timeGridDay",
              }}
              events={events}
              selectable={true}
              select={handleDateSelect}
              eventClick={handleEventClick}
              eventContent={renderEventContent}
              height="100%"
              expandRows={true}
              eventDisplay="block"
              dayMaxEvents={3}
              moreLinkClick="popover"
              slotMinTime={calendarConfig.startTime || '08:00'}
              slotMaxTime={calendarConfig.endTime || '18:00'}
              slotDuration={`00:${calendarConfig.timeSlotInterval || 30}:00`}
              customButtons={{
                addAppointmentButton: {
                  text: "New Appointment",
                  click: handleCreateAppointment,
                },
                debugTimeSlots: {
                  text: "Debug Time Slots",
                  click: () => {
                    if (calendarRef.current) {
                      const calendarApi = calendarRef.current.getApi();
                      console.log('=== DEBUG TIME SLOTS ===');
                      console.log('Current view:', calendarApi.view.type);
                      console.log('Current options:', {
                        slotMinTime: calendarApi.getOption('slotMinTime'),
                        slotMaxTime: calendarApi.getOption('slotMaxTime'),
                        slotDuration: calendarApi.getOption('slotDuration')
                      });
                      console.log('Calendar config:', calendarConfig);

                      // Switch to week view to test
                      calendarApi.changeView('timeGridWeek');
                    }
                  },
                },
              }}
            />
          )}
          </div>
        </div>

        {/* Appointment Modal */}
        <Modal
          isOpen={isOpen}
          onClose={handleCloseModal}
          className="max-w-[800px] p-0"
        >
          {viewMode === 'create' ? (
            <AppointmentForm
              selectedDate={selectedDate}
              onClose={handleCloseModal}
              onSuccess={handleAppointmentSuccess}
            />
          ) : viewMode === 'view' && selectedAppointment ? (
            <AppointmentDetails
              appointment={selectedAppointment}
              onClose={handleCloseModal}
            />
          ) : (
            <div className="p-6">
              <p className="text-gray-500 dark:text-gray-400">
                No appointment selected
              </p>
            </div>
          )}
        </Modal>

        {/* Service Modal */}
        <Modal
          isOpen={isServiceModalOpen}
          onClose={closeServiceModal}
          className="max-w-[800px] p-0"
        >
          <ServiceForm
            onClose={closeServiceModal}
            onSuccess={handleServiceSuccess}
          />
        </Modal>
      </div>
    </>
  );
};

export default Calendar;
