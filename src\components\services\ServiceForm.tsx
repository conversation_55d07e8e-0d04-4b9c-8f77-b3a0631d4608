import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import Checkbox from '../form/input/Checkbox';
import { ErrorDisplay } from '../error';
import { useCreateService, useUpdateService, useServiceCategories } from '../../hooks/useServices';
import { Service, ServiceCreateRequest } from '../../types';

// Validation schema based on documented business rules
const serviceSchema = z.object({
  title: z.string().min(1, "Service title is required").max(255, "Title too long"),
  description: z.string().max(1000, "Description too long").optional(),
  duration: z.number().int().min(1, "Duration must be at least 1 minute").max(1440, "Duration cannot exceed 24 hours"),
  price: z.number().min(0, "Price cannot be negative").max(999999.99, "Price too high"),
  pointsRequirements: z.number().int().min(0, "Points requirements cannot be negative").default(1),
  deliveryType: z.enum(['at_location', 'at_customer', 'both']),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Color must be a valid hex color"),
  serviceCategoryId: z.union([z.number(), z.undefined()]).optional(),
  isPublic: z.boolean().default(true),
  acceptOnline: z.boolean().default(true),
  acceptNew: z.boolean().default(true),
  notificationOn: z.boolean().default(true),
  servedRegions: z.string().optional(),
});

type ServiceFormData = z.infer<typeof serviceSchema>;

interface ServiceFormProps {
  service?: Service | null;
  onClose: () => void;
  onSuccess: () => void;
}

const colorOptions = [
  { value: '#3B82F6', label: 'Blue' },
  { value: '#10B981', label: 'Green' },
  { value: '#F59E0B', label: 'Yellow' },
  { value: '#EF4444', label: 'Red' },
  { value: '#8B5CF6', label: 'Purple' },
  { value: '#06B6D4', label: 'Cyan' },
  { value: '#F97316', label: 'Orange' },
  { value: '#84CC16', label: 'Lime' },
];

export default function ServiceForm({ service, onClose, onSuccess }: ServiceFormProps) {
  const isEditing = !!service;
  const { data: categories } = useServiceCategories();
  const createServiceMutation = useCreateService();
  const updateServiceMutation = useUpdateService();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<ServiceFormData>({
    resolver: zodResolver(serviceSchema),
    defaultValues: {
      title: '',
      description: '',
      duration: 30,
      price: 0,
      pointsRequirements: 1,
      deliveryType: 'at_location',
      color: '#4CAF50',
      serviceCategoryId: undefined,
      isPublic: true,
      acceptOnline: true,
      acceptNew: true,
      notificationOn: true,
      servedRegions: '',
    },
  });

  // Populate form when editing
  useEffect(() => {
    if (service) {
      reset({
        title: service.title,
        description: service.description || '',
        duration: service.duration,
        price: service.price,
        pointsRequirements: service.pointsRequirements,
        deliveryType: service.deliveryType,
        color: service.color,
        serviceCategoryId: service.serviceCategoryId || undefined,
        isPublic: service.isPublic,
        acceptOnline: service.acceptOnline,
        acceptNew: service.acceptNew,
        notificationOn: service.notificationOn,
        servedRegions: service.servedRegions?.join(', ') || '',
      });
    }
  }, [service, reset]);

  const onSubmit = async (data: ServiceFormData) => {
    console.log('Form submitted with data:', data);
    try {
      // Validate served regions for customer delivery
      if ((data.deliveryType === 'at_customer' || data.deliveryType === 'both') &&
          (!data.servedRegions || data.servedRegions.trim() === '')) {
        toast.error('Served regions are required for customer location services');
        return;
      }

      const serviceData: ServiceCreateRequest = {
        ...data,
        servedRegions: data.servedRegions
          ? data.servedRegions.split(',').map(region => region.trim()).filter(Boolean)
          : [],
      };

      console.log('Submitting service data:', serviceData);

      if (isEditing && service) {
        console.log('Updating service...');
        await updateServiceMutation.mutateAsync({
          id: service.id,
          data: serviceData,
        });
      } else {
        console.log('Creating new service...');
        await createServiceMutation.mutateAsync(serviceData);
      }

      console.log('Service operation successful, calling onSuccess');
      onSuccess();
    } catch (error) {
      console.error('Error in form submission:', error);
      // Error handled by mutations
    }
  };

  const isLoading = createServiceMutation.isPending || updateServiceMutation.isPending;
  const error = createServiceMutation.error || updateServiceMutation.error;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? 'Edit Service' : 'Create New Service'}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {isEditing ? 'Update service details' : 'Add a new service to your offerings'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit, (errors) => {
        console.log('Form validation errors:', errors);
      })} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <Label>
                  Service Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  {...register('title')}
                  placeholder="Enter service name"
                  disabled={isLoading}
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.title.message}
                  </p>
                )}
              </div>

              <div>
                <Label>
                  Duration (minutes) <span className="text-red-500">*</span>
                </Label>
                <Input
                  {...register('duration', { valueAsNumber: true })}
                  type="number"
                  min="1"
                  placeholder="60"
                  disabled={isLoading}
                />
                {errors.duration && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.duration.message}
                  </p>
                )}
              </div>

              <div>
                <Label>
                  Price <span className="text-red-500">*</span>
                </Label>
                <Input
                  {...register('price', { valueAsNumber: true })}
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  disabled={isLoading}
                />
                {errors.price && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.price.message}
                  </p>
                )}
              </div>

              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Label>Points Required</Label>
                  <div className="relative group">
                    <button
                      type="button"
                      className="w-4 h-4 rounded-full bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-400 dark:hover:bg-gray-500 flex items-center justify-center text-xs font-medium transition-colors"
                      title="Points Required Information"
                    >
                      ?
                    </button>
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
                      Credits/points customers need to book this service. Default is 1 point per booking.
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
                    </div>
                  </div>
                </div>
                <Input
                  {...register('pointsRequirements', { valueAsNumber: true })}
                  type="number"
                  min="0"
                  placeholder="0"
                  disabled={isLoading}
                />
                {errors.pointsRequirements && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.pointsRequirements.message}
                  </p>
                )}
              </div>

              <div>
                <Label>
                  Delivery Type <span className="text-red-500">*</span>
                </Label>
                <select
                  {...register('deliveryType')}
                  disabled={isLoading}
                  className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                >
                  <option value="at_location">At Location</option>
                  <option value="at_customer">At Customer</option>
                  <option value="both">Both</option>
                </select>
                {errors.deliveryType && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.deliveryType.message}
                  </p>
                )}
              </div>
            </div>

            {/* Category and Color */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label>Category</Label>
                <select
                  {...register('serviceCategoryId', {
                    setValueAs: (value) => value === '' ? undefined : Number(value)
                  })}
                  disabled={isLoading}
                  className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                >
                  <option value="">No Category</option>
                  {categories?.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.title}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <Label>
                  Color <span className="text-red-500">*</span>
                </Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {colorOptions.map((color) => (
                    <label
                      key={color.value}
                      className="flex items-center cursor-pointer"
                    >
                      <input
                        {...register('color')}
                        type="radio"
                        value={color.value}
                        className="sr-only"
                        disabled={isLoading}
                      />
                      <div
                        className={`w-8 h-8 rounded-full border-2 ${
                          watch('color') === color.value
                            ? 'border-gray-900 dark:border-white'
                            : 'border-gray-300 dark:border-gray-600'
                        }`}
                        style={{ backgroundColor: color.value }}
                        title={color.label}
                      />
                    </label>
                  ))}
                </div>
                {errors.color && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.color.message}
                  </p>
                )}
              </div>
            </div>

            {/* Description */}
            <div>
              <Label>Description</Label>
              <textarea
                {...register('description')}
                rows={3}
                placeholder="Describe your service..."
                disabled={isLoading}
                className="w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
              />
            </div>

            {/* Served Regions */}
            <div>
              <Label>Served Regions</Label>
              <Input
                {...register('servedRegions')}
                placeholder="Enter regions separated by commas (e.g., Downtown, Uptown, Suburbs)"
                disabled={isLoading}
              />
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Leave empty if service is available everywhere
              </p>
            </div>

            {/* Settings */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-800 dark:text-white">
                Service Settings
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('isPublic')}
                    onChange={(checked) => setValue('isPublic', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Public Service
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Visible to customers
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('acceptOnline')}
                    onChange={(checked) => setValue('acceptOnline', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Online Booking
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Allow online appointments
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('acceptNew')}
                    onChange={(checked) => setValue('acceptNew', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Accept New Customers
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Open to new clients
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={watch('notificationOn')}
                    onChange={(checked) => setValue('notificationOn', checked)}
                    disabled={isLoading}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Notifications
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Receive booking alerts
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {error && (
              <ErrorDisplay
                error={error}
                variant="banner"
                size="sm"
              />
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
              >
                {isLoading 
                  ? (isEditing ? 'Updating...' : 'Creating...') 
                  : (isEditing ? 'Update Service' : 'Create Service')
                }
              </Button>
            </div>
      </form>
    </div>
  );
}
