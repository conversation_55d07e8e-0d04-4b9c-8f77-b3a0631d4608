import React, { useEffect } from 'react';
import { useForm, useField<PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import Checkbox from '../form/input/Checkbox';
import { ErrorDisplay } from '../error';
import { useCreateQueue, useUpdateQueue } from '../../hooks/useQueues';
import { useLocations } from '../../hooks/useLocations';
import { useServices } from '../../hooks/useServices';
import { useAuth } from '../../context/AuthContext';
import { Queue, QueueCreateRequest } from '../../types/queue';
import { PlusIcon, TrashBinIcon, TimeIcon } from '../../icons';

// Days of the week constant
const DAYS_OF_WEEK = [
  'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
];

// Queue opening hours schema matching API
const queueOpeningHoursSchema = z.object({
  dayOfWeek: z.string().min(1, "Day of week is required"),
  isActive: z.boolean().default(true),
  hours: z.array(z.object({
    timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
    timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
  })),
});

// Validation schema matching API documentation
const queueSchema = z.object({
  title: z.string().min(1, "Queue title is required"),
  sProvidingPlaceId: z.number().int().positive("Valid location is required"),
  isActive: z.boolean().default(true),
  serviceIds: z.array(z.number().int().positive()).min(1, "At least one service must be assigned"),
  openingHours: z.array(queueOpeningHoursSchema).optional(),
});

type QueueFormData = z.infer<typeof queueSchema>;

interface QueueFormProps {
  queue?: Queue | null;
  onClose: () => void;
  onSuccess: () => void;
}

export default function QueueForm({ queue, onClose, onSuccess }: QueueFormProps) {
  const isEditing = !!queue;
  const { isAuthenticated, token } = useAuth();
  const createQueueMutation = useCreateQueue();
  const updateQueueMutation = useUpdateQueue();
  const { data: locations } = useLocations();
  const { data: services } = useServices();

  const isLoading = createQueueMutation.isPending || updateQueueMutation.isPending;
  const error = createQueueMutation.error || updateQueueMutation.error;

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
    control,
    getValues,
  } = useForm<QueueFormData>({
    resolver: zodResolver(queueSchema),
    defaultValues: {
      title: '',
      sProvidingPlaceId: 0,
      isActive: true,
      serviceIds: [],
      openingHours: [],
    },
  });

  // Field array for opening hours
  const { fields: openingHoursFields, append: appendOpeningHour, remove: removeOpeningHour } = useFieldArray({
    control,
    name: 'openingHours',
  });

  // Helper functions for opening hours
  const addOpeningHour = () => {
    const existingDays = openingHoursFields.map((_, index) => watch(`openingHours.${index}.dayOfWeek`));
    const availableDays = DAYS_OF_WEEK.filter(day => !existingDays.includes(day));
    const nextDay = availableDays.length > 0 ? availableDays[0] : 'Monday';

    appendOpeningHour({
      dayOfWeek: nextDay,
      isActive: true,
      hours: [{ timeFrom: '09:00', timeTo: '17:00' }],
    });
  };

  const addTimeSlot = (dayIndex: number) => {
    const currentHours = watch(`openingHours.${dayIndex}.hours`) || [];
    setValue(`openingHours.${dayIndex}.hours`, [
      ...currentHours,
      { timeFrom: '09:00', timeTo: '17:00' }
    ]);
  };

  const removeTimeSlot = (dayIndex: number, hourIndex: number) => {
    const currentHours = watch(`openingHours.${dayIndex}.hours`) || [];
    const newHours = currentHours.filter((_, index) => index !== hourIndex);
    setValue(`openingHours.${dayIndex}.hours`, newHours);
  };

  const setupStandardHours = () => {
    const standardHours = [
      { dayOfWeek: 'Monday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Tuesday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Wednesday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Thursday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Friday', isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] },
      { dayOfWeek: 'Saturday', isActive: false, hours: [] },
      { dayOfWeek: 'Sunday', isActive: false, hours: [] },
    ];
    setValue('openingHours', standardHours);
  };

  // Populate form when editing
  useEffect(() => {
    if (queue) {
      reset({
        title: queue.title,
        sProvidingPlaceId: queue.sProvidingPlaceId,
        isActive: queue.isActive,
        serviceIds: queue.services?.map(s => s.id) || [],
        openingHours: queue.openings?.map(opening => ({
          dayOfWeek: opening.dayOfWeek,
          isActive: opening.isActive,
          hours: opening.hours?.map(hour => ({
            timeFrom: hour.timeFrom,
            timeTo: hour.timeTo,
          })) || [],
        })) || [],
      });
    }
  }, [queue, reset]);

  const onSubmit = async (data: QueueFormData) => {
    console.log('🚀 Queue form submission started:', data);

    // Check authentication before proceeding
    if (!isAuthenticated || !token) {
      console.error('❌ User not authenticated');
      alert('Session expired. Please log in again.');
      return;
    }

    console.log('✅ User authenticated, proceeding with submission');

    try {
      const queueData: QueueCreateRequest = {
        title: data.title,
        sProvidingPlaceId: data.sProvidingPlaceId,
        isActive: data.isActive,
        serviceIds: data.serviceIds,
        openingHours: data.openingHours && data.openingHours.length > 0 ? data.openingHours : undefined,
      };

      console.log('📝 Queue data prepared:', queueData);

      if (isEditing && queue) {
        console.log('✏️ Updating existing queue:', queue.id);
        await updateQueueMutation.mutateAsync({
          id: queue.id,
          ...queueData,
        });
      } else {
        console.log('➕ Creating new queue');
        await createQueueMutation.mutateAsync(queueData);
      }

      console.log('✅ Queue operation completed successfully');
      onSuccess();
    } catch (error) {
      console.error('❌ Queue form submission error:', error);
      // Error handled by mutation
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? 'Edit Queue' : 'Create New Queue'}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {isEditing ? 'Update queue details and settings' : 'Set up a new customer queue for your location'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-8">
        {/* Basic Information */}
        <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Basic Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <Label>
                Queue Title <span className="text-red-500">*</span>
              </Label>
              <Input
                {...register('title')}
                placeholder="Enter queue title"
                disabled={isLoading}
              />
              {errors.title && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.title.message}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Location & Services */}
        <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Location & Services
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label>
                Location <span className="text-red-500">*</span>
              </Label>
              <select
                {...register('sProvidingPlaceId', { valueAsNumber: true })}
                disabled={isLoading}
                className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
              >
                <option value={0}>Select a location</option>
                {locations?.map((location) => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
              {errors.sProvidingPlaceId && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.sProvidingPlaceId.message}
                </p>
              )}
            </div>

            <div>
              <Label>
                Services <span className="text-red-500">*</span>
              </Label>
              <Controller
                name="serviceIds"
                control={control}
                render={({ field }) => (
                  <div className="space-y-2 max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-700 rounded-lg p-3">
                    {services?.map((service) => (
                      <label key={service.id} className="flex items-center">
                        <Checkbox
                          checked={field.value?.includes(service.id) || false}
                          onChange={(checked) => {
                            const currentValue = field.value || [];
                            if (checked) {
                              field.onChange([...currentValue, service.id]);
                            } else {
                              field.onChange(currentValue.filter((id: number) => id !== service.id));
                            }
                          }}
                          disabled={isLoading}
                        />
                        <span className="ml-2 text-sm text-gray-900 dark:text-white">
                          {service.title}
                        </span>
                      </label>
                    ))}
                  </div>
                )}
              />
              {errors.serviceIds && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.serviceIds.message}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Queue Settings */}
        <div className="pb-6 border-b border-gray-100 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Queue Settings
          </h3>
          <div className="flex items-center">
            <Checkbox
              {...register('isActive')}
              id="isActive"
              disabled={isLoading}
            />
            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900 dark:text-white">
              Queue is active
            </label>
          </div>
        </div>

        {/* Opening Hours */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Opening Hours
            </h3>
            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={setupStandardHours}
                startIcon={<TimeIcon className="w-4 h-4" />}
              >
                Standard Hours
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addOpeningHour}
                startIcon={<PlusIcon className="w-4 h-4" />}
              >
                Add Day
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            {openingHoursFields.map((field, dayIndex) => (
              <div key={field.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Controller
                      name={`openingHours.${dayIndex}.dayOfWeek`}
                      control={control}
                      render={({ field }) => (
                        <select
                          {...field}
                          className="rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                        >
                          {DAYS_OF_WEEK.map((day) => (
                            <option key={day} value={day}>
                              {day}
                            </option>
                          ))}
                        </select>
                      )}
                    />
                    <Controller
                      name={`openingHours.${dayIndex}.isActive`}
                      control={control}
                      render={({ field }) => (
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={field.value}
                            onChange={field.onChange}
                            className="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300 rounded"
                          />
                          <span className="ml-2 text-sm text-gray-900 dark:text-white">Active</span>
                        </label>
                      )}
                    />
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeOpeningHour(dayIndex)}
                    startIcon={<TrashBinIcon className="w-4 h-4" />}
                  >
                    Remove
                  </Button>
                </div>

                {/* Time Slots */}
                <div className="space-y-2">
                  {(watch(`openingHours.${dayIndex}.hours`) || []).map((_, hourIndex) => (
                    <div key={hourIndex} className="flex items-center space-x-2">
                      <Controller
                        name={`openingHours.${dayIndex}.hours.${hourIndex}.timeFrom`}
                        control={control}
                        render={({ field }) => (
                          <input
                            {...field}
                            type="time"
                            className="rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                          />
                        )}
                      />
                      <span className="text-gray-500">to</span>
                      <Controller
                        name={`openingHours.${dayIndex}.hours.${hourIndex}.timeTo`}
                        control={control}
                        render={({ field }) => (
                          <input
                            {...field}
                            type="time"
                            className="rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                          />
                        )}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeTimeSlot(dayIndex, hourIndex)}
                        startIcon={<TrashBinIcon className="w-4 h-4" />}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => addTimeSlot(dayIndex)}
                    startIcon={<PlusIcon className="w-4 h-4" />}
                  >
                    Add Time Slot
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createQueueMutation.isPending || updateQueueMutation.isPending}
          >
            {(createQueueMutation.isPending || updateQueueMutation.isPending)
              ? 'Submitting...'
              : (isEditing ? 'Update Queue' : 'Create Queue')
            }
          </Button>
        </div>
      </form>
    </div>
  );
}
